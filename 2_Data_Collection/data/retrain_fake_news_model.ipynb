{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Fake News Detection Model Retraining\n",
    "This notebook combines real and synthetic data, preprocesses it, splits it for robust validation, retrains the model, and saves the best model and vectorizer."
   ]
  },
  {
   "cell_type": "code",
   "metadata": {},
   "source": [
    "import pandas as pd\n",
    "import numpy as np\n",
    "from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score\n",
    "from sklearn.feature_extraction.text import TfidfVectorizer\n",
    "from sklearn.linear_model import LogisticRegression\n",
    "from sklearn.ensemble import RandomForestClassifier\n",
    "from sklearn.metrics import classification_report, confusion_matrix\n",
    "import joblib\n",
    "import os\n",
    "from datetime import datetime\n"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Load and Combine Datasets"
   ]
  },
  {
   "cell_type": "code",
   "metadata": {},
   "source": [
    "# Paths\n",
    "data_dir = '../../2_Data_Collection/data/'\n",
    "real_files = ['True.csv', 'synthetic_true.csv']\n",
    "fake_files = ['Fake.csv', 'synthetic_fake.csv']\n",
    "\n",
    "real_dfs = [pd.read_csv(os.path.join(data_dir, f)) for f in real_files if os.path.exists(os.path.join(data_dir, f))]\n",
    "fake_dfs = [pd.read_csv(os.path.join(data_dir, f)) for f in fake_files if os.path.exists(os.path.join(data_dir, f))]\n",
    "\n",
    "real_df = pd.concat(real_dfs, ignore_index=True) if real_dfs else pd.DataFrame()\n",
    "fake_df = pd.concat(fake_dfs, ignore_index=True) if fake_dfs else pd.DataFrame()\n",
    "\n",
    "real_df['label'] = 1\n",
    "fake_df['label'] = 0\n",
    "\n",
    "df = pd.concat([real_df, fake_df], ignore_index=True)\n",
    "print(f'Total samples: {len(df)} | Real: {len(real_df)} | Fake: {len(fake_df)}')\n",
    "df = df.drop_duplicates(subset='text').sample(frac=1, random_state=42).reset_index(drop=True)  # Shuffle\n"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Preprocessing"
   ]
  },
  {
   "cell_type": "code",
   "metadata": {},
   "source": [
    "import re\n",
    "def clean_text(text):\n",
    "    text = str(text)\n",
    "    text = re.sub(r'\\s+', ' ', text)\n",
    "    text = re.sub(r'[^a-zA-Z0-9 .,?!\'\"]', '', text)\n",
    "    return text.strip().lower()\n",
    "\n",
    "df['text'] = df['text'].apply(clean_text)\n",
    "df = df[df['text'].str.len() > 30]  # Remove very short articles\n",
    "df = df.dropna(subset=['text'])\n",
    "print('After cleaning:', len(df))\n"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Train/Validation/Test Split"
   ]
  },
  {
   "cell_type": "code",
   "metadata": {},
   "source": [
    "train_df, temp_df = train_test_split(df, test_size=0.3, stratify=df['label'], random_state=42)\n",
    "val_df, test_df = train_test_split(temp_df, test_size=0.5, stratify=temp_df['label'], random_state=42)\n",
    "print(f'Train: {len(train_df)}, Val: {len(val_df)}, Test: {len(test_df)}')\n"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## TF-IDF Vectorization"
   ]
  },
  {
   "cell_type": "code",
   "metadata": {},
   "source": [
    "vectorizer = TfidfVectorizer(max_features=5000, ngram_range=(1,2))\n",
    "X_train = vectorizer.fit_transform(train_df['text'])\n",
    "X_val = vectorizer.transform(val_df['text'])\n",
    "X_test = vectorizer.transform(test_df['text'])\n",
    "y_train = train_df['label']\n",
    "y_val = val_df['label']\n",
    "y_test = test_df['label']\n"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Train and Validate Models"
   ]
  },
  {
   "cell_type": "code",
   "metadata": {},
   "source": [
    "# Logistic Regression\n",
    "lr = LogisticRegression(max_iter=1000)\n",
    "lr.fit(X_train, y_train)\n",
    "val_preds = lr.predict(X_val)\n",
    "print('Logistic Regression Validation:')\n",
    "print(classification_report(y_val, val_preds))\n",
    "print('Confusion matrix:')\n",
    "print(confusion_matrix(y_val, val_preds))\n"
   ]
  },
  {
   "cell_type": "code",
   "metadata": {},
   "source": [
    "# Random Forest\n",
    "rf = RandomForestClassifier(n_estimators=200, random_state=42)\n",
    "rf.fit(X_train, y_train)\n",
    "val_preds_rf = rf.predict(X_val)\n",
    "print('Random Forest Validation:')\n",
    "print(classification_report(y_val, val_preds_rf))\n",
    "print('Confusion matrix:')\n",
    "print(confusion_matrix(y_val, val_preds_rf))\n"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Test Set Evaluation (Best Model)"
   ]
  },
  {
   "cell_type": "code",
   "metadata": {},
   "source": [
    "test_preds = lr.predict(X_test)\n",
    "print('Logistic Regression Test:')\n",
    "print(classification_report(y_test, test_preds))\n",
    "print('Confusion matrix:')\n",
    "print(confusion_matrix(y_test, test_preds))\n"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Save Model and Vectorizer"
   ]
  },
  {
   "cell_type": "code",
   "metadata": {},
   "source": [
    "model_dir = '../../streamlit_app/models/'\n",
    "os.makedirs(model_dir, exist_ok=True)\n",
    "joblib.dump(lr, os.path.join(model_dir, 'logistic_regression_model.pkl'))\n",
    "joblib.dump(rf, os.path.join(model_dir, 'random_forest_model.pkl'))\n",
    "joblib.dump(vectorizer, os.path.join(model_dir, 'tfidf_vectorizer.pkl'))\n",
    "print('Models and vectorizer saved!')\n"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "name": "python",
   "version": "3.8"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 2
}
