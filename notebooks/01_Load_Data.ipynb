{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Jupyter Notebook is working!\n"]}], "source": ["print(\"Jupyter Notebook is working!\")\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                               title  \\\n", "0  As U.S. budget fight looms, Republicans flip t...   \n", "1  U.S. military to accept transgender recruits o...   \n", "2  Senior U.S. Republican senator: 'Let Mr. <PERSON>...   \n", "3  FBI Russia probe helped by Australian diplomat...   \n", "4  Trump wants Postal Service to charge 'much mor...   \n", "\n", "                                                text       subject  \\\n", "0  WASHINGTON (Reuters) - The head of a conservat...  politicsNews   \n", "1  WASHINGTON (Reuters) - Transgender people will...  politicsNews   \n", "2  WASHINGTON (Reuters) - The special counsel inv...  politicsNews   \n", "3  WASHINGTON (Reuters) - Trump campaign adviser ...  politicsNews   \n", "4  SEATTLE/WASHINGTON (Reuters) - President <PERSON><PERSON>...  politicsNews   \n", "\n", "                 date  label  \n", "0  December 31, 2017       1  \n", "1  December 29, 2017       1  \n", "2  December 31, 2017       1  \n", "3  December 30, 2017       1  \n", "4  December 29, 2017       1  \n"]}], "source": ["import pandas as pd\n", "\n", "# Load Kaggle Fake News Dataset\n", "df_real = pd.read_csv(\"../data/True.csv\")  # Real news\n", "df_fake = pd.read_csv(\"../data/Fake.csv\")  # Fake news\n", "\n", "# Add labels: 1 = Real, 0 = Fake\n", "df_real['label'] = 1\n", "df_fake['label'] = 0\n", "\n", "# Merge datasets\n", "df = pd.concat([df_real, df_fake], ignore_index=True)\n", "\n", "# Display first 5 rows\n", "print(df.head())\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                               title  \\\n", "0  As U.S. budget fight looms, Republicans flip t...   \n", "1  U.S. military to accept transgender recruits o...   \n", "2  Senior U.S. Republican senator: 'Let Mr. <PERSON>...   \n", "3  FBI Russia probe helped by Australian diplomat...   \n", "4  Trump wants Postal Service to charge 'much mor...   \n", "\n", "                                                text       subject  \\\n", "0  WASHINGTON (Reuters) - The head of a conservat...  politicsNews   \n", "1  WASHINGTON (Reuters) - Transgender people will...  politicsNews   \n", "2  WASHINGTON (Reuters) - The special counsel inv...  politicsNews   \n", "3  WASHINGTON (Reuters) - Trump campaign adviser ...  politicsNews   \n", "4  SEATTLE/WASHINGTON (Reuters) - President <PERSON><PERSON>...  politicsNews   \n", "\n", "                 date  label  \n", "0  December 31, 2017       1  \n", "1  December 29, 2017       1  \n", "2  December 31, 2017       1  \n", "3  December 30, 2017       1  \n", "4  December 29, 2017       1  \n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 44898 entries, 0 to 44897\n", "Data columns (total 5 columns):\n", " #   Column   Non-Null Count  Dtype \n", "---  ------   --------------  ----- \n", " 0   title    44898 non-null  object\n", " 1   text     44898 non-null  object\n", " 2   subject  44898 non-null  object\n", " 3   date     44898 non-null  object\n", " 4   label    44898 non-null  int64 \n", "dtypes: int64(1), object(4)\n", "memory usage: 1.7+ MB\n", "None\n", "title      0\n", "text       0\n", "subject    0\n", "date       0\n", "label      0\n", "dtype: int64\n", "label\n", "0    23481\n", "1    21417\n", "Name: count, dtype: int64\n"]}], "source": ["import pandas as pd\n", "\n", "# Load datasets\n", "df_real = pd.read_csv(\"../data/True.csv\")  # Real news\n", "df_fake = pd.read_csv(\"../data/Fake.csv\")  # Fake news\n", "\n", "# Add labels: 1 = Real, 0 = Fake\n", "df_real['label'] = 1\n", "df_fake['label'] = 0\n", "\n", "# Merge datasets\n", "df = pd.concat([df_real, df_fake], ignore_index=True)\n", "\n", "# Display first 5 rows\n", "print(df.head())\n", "\n", "# Check dataset info\n", "print(df.info())\n", "\n", "# Check for missing values\n", "print(df.isnull().sum())\n", "\n", "# Check class distribution\n", "print(df['label'].value_counts())\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                               title  \\\n", "0  As U.S. budget fight looms, Republicans flip t...   \n", "1  U.S. military to accept transgender recruits o...   \n", "2  Senior U.S. Republican senator: 'Let Mr. <PERSON>...   \n", "3  FBI Russia probe helped by Australian diplomat...   \n", "4  Trump wants Postal Service to charge 'much mor...   \n", "\n", "                                                text       subject  \\\n", "0  washington reuters the head of a conservative ...  politicsNews   \n", "1  washington reuters transgender people will be ...  politicsNews   \n", "2  washington reuters the special counsel investi...  politicsNews   \n", "3  washington reuters trump campaign adviser geor...  politicsNews   \n", "4  seattlewashington reuters president donald tru...  politicsNews   \n", "\n", "                 date  label  \n", "0  December 31, 2017       1  \n", "1  December 29, 2017       1  \n", "2  December 31, 2017       1  \n", "3  December 30, 2017       1  \n", "4  December 29, 2017       1  \n"]}], "source": ["import re\n", "\n", "# Function to clean text\n", "def clean_text(text):\n", "    text = re.sub(r'https?://\\S+', '', text)  # Remove URLs\n", "    text = re.sub(r'[^a-zA-Z ]', '', text)  # Remove special characters\n", "    text = re.sub(r'\\s+', ' ', text).strip()  # Remove extra spaces\n", "    return text.lower()\n", "\n", "# Apply cleaning function to the text column\n", "df['text'] = df['text'].apply(clean_text)\n", "\n", "# Display cleaned text\n", "print(df.head())\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cleaned dataset saved successfully!\n"]}], "source": ["# Save cleaned dataset\n", "df.to_csv(\"../data/cleaned_news.csv\", index=False)\n", "print(\"Cleaned dataset saved successfully!\")\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}