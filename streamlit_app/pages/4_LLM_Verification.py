import os
import sys
import streamlit as st

# Add the parent directory to the path so we can import the llm_verification module
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Add the streamlit_app directory to the path for shared modules
streamlit_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if streamlit_dir not in sys.path:
    sys.path.append(streamlit_dir)

# Import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared_styles import load_css
from shared_components import hide_sidebar_items

# Import the LLM verification module
try:
    from llm_verification.news_verifier import NewsVerifier
    from llm_verification.ollama_client import OllamaClient
except ImportError as e:
    st.error(f"Failed to import LLM verification modules: {e}")
    st.stop()

# Load CSS and hide sidebar items
load_css()
hide_sidebar_items()

# Page configuration
st.set_page_config(
    page_title="LLM Verification",
    page_icon="🤖",
    layout="wide"
)

st.title("🤖 LLM News Verification")
st.markdown("Verify news articles using a local Large Language Model combined with web search.")

# Sidebar for model selection
st.sidebar.header("🔧 Configuration")

# Get available models
try:
    ollama_client = OllamaClient()
    available_models = ollama_client.list_models()
    if not available_models:
        st.sidebar.error("No Ollama models found. Please install a model first.")
        st.sidebar.code("ollama pull deepseek-r1:7b")
        st.stop()
except Exception as e:
    st.sidebar.error(f"Error connecting to Ollama: {e}")
    st.stop()

# Model selection
model_name = st.sidebar.selectbox(
    "Select Model",
    available_models,
    index=0 if available_models else 0,
    help="Choose the LLM model for verification"
)

# Temperature setting
temperature = st.sidebar.slider(
    "Temperature",
    min_value=0.0,
    max_value=1.0,
    value=0.1,
    step=0.1,
    help="Controls randomness in model responses"
)

# Main content area
st.markdown("### 📝 Enter Article Text")
text_input = st.text_area(
    "Paste the news article text here:",
    height=200,
    placeholder="Enter the news article text you want to verify..."
)

# Verify button
verify_button = st.button("🔍 Verify Article", type="primary")

if verify_button and text_input:
    st.write("🚀 VERIFICATION STARTED")

    # Simple progress indicator
    progress_bar = st.progress(0)

    try:
        st.write("📝 Initializing verifier...")
        progress_bar.progress(25)

        # Initialize verifier
        verifier = NewsVerifier(model_name=model_name, temperature=temperature)
        st.write("✅ Verifier initialized successfully!")

        st.write("🔍 Running verification...")
        progress_bar.progress(50)

        # Run verification
        st.write("🔄 Calling verify_article method...")
        result = verifier.verify_article(text_input)
        st.write("🔄 verify_article method returned!")

        st.write("✅ Verification complete!")
        progress_bar.progress(100)

        # IMMEDIATE DISPLAY - NO FANCY FORMATTING
        try:
            st.write("🔍 DEBUG: Checking result...")
            st.write(f"Result type: {type(result)}")
            st.write(f"Result is truthy: {bool(result)}")

            if result:
                st.success("🎉 RESULTS RECEIVED!")

                # DEBUG: Show the raw result
                st.write("🔍 DEBUG: Raw result keys:")
                if isinstance(result, dict):
                    st.write(list(result.keys()))
                    st.write("🔍 DEBUG: Raw result:")
                    st.json(result)
                else:
                    st.write(f"Result is not a dict, it's: {type(result)}")
                    st.write(str(result))

                # Display results immediately
                st.header("📊 Verification Results")

            # Basic info
            verdict = result.get('verdict', 'Unknown')
            confidence = result.get('confidence', 'Unknown')
            real_pct = result.get('real_percentage', 50)
            fake_pct = result.get('fake_percentage', 50)
            analysis = result.get('analysis', 'No analysis available')

            # Display verdict
            if verdict == 'Real':
                st.success(f"✅ VERDICT: {verdict}")
            elif verdict == 'Fake':
                st.error(f"❌ VERDICT: {verdict}")
            else:
                st.warning(f"⚠️ VERDICT: {verdict}")

            # Display confidence
            st.info(f"🎯 CONFIDENCE: {confidence}")

            # Display percentages
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Real", f"{real_pct}%")
            with col2:
                st.metric("Fake", f"{fake_pct}%")

            # Display analysis
            st.subheader("📝 Analysis")
            st.write(analysis)

            # Display claims
            claims = result.get('claims', [])
            if claims:
                st.subheader("🔍 Key Claims")
                for i, claim in enumerate(claims, 1):
                    st.write(f"{i}. {claim}")

                # Display search results
                search_results = result.get('search_results', [])
                if search_results:
                    st.subheader("🌐 Search Results")
                    for i, sr in enumerate(search_results, 1):
                        with st.expander(f"Claim {i} Results"):
                            st.write(f"**Claim:** {sr.get('claim', 'N/A')}")
                            st.write(f"**Results:** {sr.get('search_results', 'N/A')}")
            else:
                st.error("❌ No results received from verification!")

        except Exception as result_error:
            st.error(f"🚨 ERROR PROCESSING RESULTS: {str(result_error)}")
            import traceback
            st.code(traceback.format_exc())

    except Exception as e:
        st.error(f"🚨 ERROR: {str(e)}")
        import traceback
        st.code(traceback.format_exc())

# Information about the feature
with st.expander("About LLM Verification", expanded=False):
    st.markdown("""
    ### 🤖 How LLM Verification Works

    This feature uses a locally running Large Language Model (LLM) combined with web search to verify news articles:

    1. **Key Claim Extraction**: The LLM identifies the main factual claims in the article
    2. **Web Search**: Each claim is searched on the web to find supporting or contradicting information
    3. **Analysis**: The LLM analyzes the search results and compares them with the article
    4. **Verdict**: Based on the analysis, the LLM provides a verdict on whether the article appears to be real or fake

    ### 🔒 Privacy & Performance
    - All LLM processing happens locally on your machine
    - No article data is sent to external AI services
    - Performance depends on your hardware and the model size
    - First-time model loading may take a few moments

    ### ⚠️ Limitations
    - The system is not perfect and should be used as one tool among many for fact-checking
    - Results depend on the quality of web search results
    - The LLM may occasionally hallucinate or make errors in its analysis
    - Always verify important information with multiple trusted sources
    """)
