import os
import sys
import streamlit as st

# Add the parent directory to the path so we can import the llm_verification module
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Add the streamlit_app directory to the path for shared modules
streamlit_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if streamlit_dir not in sys.path:
    sys.path.append(streamlit_dir)

# Import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared_styles import load_css
from shared_components import hide_sidebar_items

# Import the LLM verification module
try:
    from llm_verification.news_verifier import NewsVerifier
    from llm_verification.ollama_client import OllamaClient
except ImportError as e:
    st.error(f"Failed to import LLM verification modules: {e}")
    st.stop()

# Load CSS and hide sidebar items
load_css()
hide_sidebar_items()

# Page configuration
st.set_page_config(
    page_title="LLM Verification",
    page_icon="🤖",
    layout="wide"
)

st.title("🤖 LLM News Verification")
st.markdown("Verify news articles using a local Large Language Model combined with web search.")

# Sidebar for model selection
st.sidebar.header("🔧 Configuration")

# Get available models
try:
    ollama_client = OllamaClient()
    available_models = ollama_client.list_models()
    if not available_models:
        st.sidebar.error("No Ollama models found. Please install a model first.")
        st.sidebar.code("ollama pull deepseek-r1:7b")
        st.stop()
except Exception as e:
    st.sidebar.error(f"Error connecting to Ollama: {e}")
    st.stop()

# Model selection
model_name = st.sidebar.selectbox(
    "Select Model",
    available_models,
    index=0 if available_models else 0,
    help="Choose the LLM model for verification"
)

# Temperature setting
temperature = st.sidebar.slider(
    "Temperature",
    min_value=0.0,
    max_value=1.0,
    value=0.1,
    step=0.1,
    help="Controls randomness in model responses"
)

# Main content area
st.markdown("### 📝 Enter Article Text")
text_input = st.text_area(
    "Paste the news article text here:",
    height=200,
    placeholder="Enter the news article text you want to verify..."
)

# Verify button
verify_button = st.button("🔍 Verify Article", type="primary")

if verify_button and text_input:
    with st.spinner("Running verification..."):
        try:
            # Initialize verifier
            verifier = NewsVerifier(model_name=model_name, temperature=temperature)
            
            # Run verification
            result = verifier.verify_article(text_input)
            
            # FORCE DISPLAY RESULTS
            st.success("✅ Verification Complete!")
            
            # Extract data with defaults
            verdict = result.get('verdict', 'Unknown') if result else 'Error'
            confidence = result.get('confidence', 'Unknown') if result else 'Unknown'
            real_pct = result.get('real_percentage', 50) if result else 50
            fake_pct = result.get('fake_percentage', 50) if result else 50
            analysis = result.get('analysis', 'No analysis available') if result else 'No analysis available'
            claims = result.get('claims', []) if result else []
            search_results = result.get('search_results', []) if result else []
            
            # Display verdict with color
            if verdict == 'Real':
                st.success(f"✅ **VERDICT: {verdict}**")
            elif verdict == 'Fake':
                st.error(f"❌ **VERDICT: {verdict}**")
            else:
                st.warning(f"⚠️ **VERDICT: {verdict}**")
            
            # Display confidence and percentages
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Confidence", confidence)
            with col2:
                st.metric("Real", f"{real_pct}%")
            with col3:
                st.metric("Fake", f"{fake_pct}%")
            
            # Display analysis/explanation
            st.subheader("📝 Analysis & Explanation")
            st.write(analysis)
            
            # Display claims
            if claims:
                st.subheader("🔍 Key Claims Identified")
                for i, claim in enumerate(claims, 1):
                    st.write(f"**{i}.** {claim}")
            
            # Display search results
            if search_results:
                st.subheader("🌐 Web Search Verification")
                for i, sr in enumerate(search_results, 1):
                    with st.expander(f"Verification for Claim {i}"):
                        st.write(f"**Claim:** {sr.get('claim', 'N/A')}")
                        st.write(f"**Search Results:** {sr.get('search_results', 'N/A')}")
            
        except Exception as e:
            st.error(f"❌ **Error during verification:** {str(e)}")
            st.code(str(e))

# Information section
with st.expander("ℹ️ About This Feature"):
    st.markdown("""
    **How it works:**
    1. Extracts key factual claims from the article
    2. Searches the web to verify each claim
    3. Uses AI to analyze the search results
    4. Provides a verdict on the article's credibility
    
    **Note:** This is an AI-powered tool. Always verify important information with multiple trusted sources.
    """)
