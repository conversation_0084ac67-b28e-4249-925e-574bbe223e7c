{"cells": [{"cell_type": "code", "execution_count": 12, "id": "185e30a5-67cf-4b82-b284-af9b1b7da440", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading required NLTK data...\n", "NLTK data download completed!\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package stopwords to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package stopwords is already up-to-date!\n", "[nltk_data] Downloading package wordnet to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package wordnet is already up-to-date!\n", "[nltk_data] Downloading package omw-1.4 to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package omw-1.4 is already up-to-date!\n"]}], "source": ["# Part 1: Initial Setup and Imports\n", "import pandas as pd\n", "import numpy as np\n", "import re\n", "import nltk\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Set up NLTK data directory\n", "import os\n", "nltk_data_dir = os.path.expanduser('~/nltk_data')\n", "if not os.path.exists(nltk_data_dir):\n", "    os.makedirs(nltk_data_dir)\n", "\n", "# Download NLTK data with SSL handling\n", "import ssl\n", "try:\n", "    _create_unverified_https_context = ssl._create_unverified_context\n", "except AttributeError:\n", "    pass\n", "else:\n", "    ssl._create_default_https_context = _create_unverified_https_context\n", "\n", "# Download required NLTK packages\n", "print(\"Downloading required NLTK data...\")\n", "for package in ['punkt', 'stopwords', 'wordnet', 'omw-1.4']:\n", "    nltk.download(package, download_dir=nltk_data_dir)\n", "print(\"NLTK data download completed!\")\n", "\n", "# Now import NLTK-specific modules\n", "from nltk.corpus import stopwords\n", "from nltk.tokenize import word_tokenize\n", "from nltk.stem import WordNetLemmatizer"]}, {"cell_type": "code", "execution_count": 13, "id": "2f18f633-0ae2-4f79-b926-98e72376408b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading datasets...\n", "\n", "Fake News Dataset Info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 23481 entries, 0 to 23480\n", "Data columns (total 4 columns):\n", " #   Column   Non-Null Count  Dtype \n", "---  ------   --------------  ----- \n", " 0   title    23481 non-null  object\n", " 1   text     23481 non-null  object\n", " 2   subject  23481 non-null  object\n", " 3   date     23481 non-null  object\n", "dtypes: object(4)\n", "memory usage: 733.9+ KB\n", "None\n", "\n", "True News Dataset Info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 21417 entries, 0 to 21416\n", "Data columns (total 4 columns):\n", " #   Column   Non-Null Count  Dtype \n", "---  ------   --------------  ----- \n", " 0   title    21417 non-null  object\n", " 1   text     21417 non-null  object\n", " 2   subject  21417 non-null  object\n", " 3   date     21417 non-null  object\n", "dtypes: object(4)\n", "memory usage: 669.4+ KB\n", "None\n", "\n", "Sample of Fake News:\n", "                                               title  \\\n", "0   <PERSON> Sends Out Embarrassing New Year’...   \n", "1   Drunk Bragging <PERSON> Staffer Started Russian ...   \n", "\n", "                                                text  \n", "0  <PERSON> just couldn t wish all Americans ...  \n", "1  House Intelligence Committee Chairman <PERSON>...  \n", "\n", "<PERSON><PERSON> of True News:\n", "                                               title  \\\n", "0  As U.S. budget fight looms, Republicans flip t...   \n", "1  U.S. military to accept transgender recruits o...   \n", "\n", "                                                text  \n", "0  WASHINGTON (Reuters) - The head of a conservat...  \n", "1  WASHINGTON (Reuters) - Transgender people will...  \n"]}], "source": ["# Part 2: Data Loading and Initial Examination\n", "def load_and_examine_data():\n", "    # Load datasets\n", "    print(\"Loading datasets...\")\n", "    fake_df = pd.read_csv('../2_Data_Collection/data/Fake.csv')\n", "    true_df = pd.read_csv('../2_Data_Collection/data/True.csv')\n", "    \n", "    # Display basic information\n", "    print(\"\\nFake News Dataset Info:\")\n", "    print(fake_df.info())\n", "    print(\"\\nTrue News Dataset Info:\")\n", "    print(true_df.info())\n", "    \n", "    # Display sample rows\n", "    print(\"\\nSample of Fake News:\")\n", "    print(fake_df[['title', 'text']].head(2))\n", "    print(\"\\nSample of True News:\")\n", "    print(true_df[['title', 'text']].head(2))\n", "    \n", "    return fake_df, true_df\n", "\n", "# Load the data\n", "fake_df, true_df = load_and_examine_data()"]}, {"cell_type": "code", "execution_count": 14, "id": "529f63e0-4335-42ad-bad7-6d38e4775605", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original: This is a test sentence! With some numbers 123 and special characters @#$. http://example.com <EMAIL>\n", "Preprocessed: test sentence number special character\n"]}], "source": ["# Part 3: Text Preprocessing Function\n", "def preprocess_text(text):\n", "    \"\"\"\n", "    Function to clean and preprocess text data\n", "    \"\"\"\n", "    try:\n", "        if not isinstance(text, str):\n", "            return ''\n", "            \n", "        # Convert to lowercase\n", "        text = text.lower()\n", "        \n", "        # Remove URLs\n", "        text = re.sub(r'http\\S+|www\\S+|https\\S+', '', text, flags=re.MULTILINE)\n", "        \n", "        # Remove email addresses\n", "        text = re.sub(r'\\S+@\\S+', '', text)\n", "        \n", "        # Remove special characters and digits\n", "        text = re.sub(r'[^\\w\\s]', '', text)\n", "        text = re.sub(r'\\d+', '', text)\n", "        \n", "        # Tokenization\n", "        tokens = text.split()\n", "        \n", "        # Remove stopwords\n", "        stop_words = set(stopwords.words('english'))\n", "        tokens = [token for token in tokens if token not in stop_words]\n", "        \n", "        # Lemmatization\n", "        lemmatizer = WordNetLemmatizer()\n", "        tokens = [lemmatizer.lemmatize(token) for token in tokens]\n", "        \n", "        # Join tokens back into text\n", "        return ' '.join(tokens)\n", "    except Exception as e:\n", "        print(f\"Error in preprocessing: {e}\")\n", "        return text\n", "\n", "# Test the preprocessing function\n", "test_text = \"This is a test sentence! With some numbers 123 and special characters @#$. http://example.com <EMAIL>\"\n", "print(\"Original:\", test_text)\n", "print(\"Preprocessed:\", preprocess_text(test_text))"]}, {"cell_type": "code", "execution_count": 21, "id": "133ba2b4-8552-4b85-9ec8-1e176f8eefff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current working directory: /Users/<USER>/Documents/FakeNewsDetection_Development/3_Data_Analysis\n", "Found datasets at:\n", "/Users/<USER>/Documents/FakeNewsDetection_Development/3_Data_Analysis/../2_Data_Collection/data/Fake.csv\n", "/Users/<USER>/Documents/FakeNewsDetection_Development/3_Data_Analysis/../2_Data_Collection/data/True.csv\n", "Loading datasets...\n", "Processing text data...\n", "Filtering dataset...\n", "Final dataset shape after filtering: (38043, 7)\n", "\n", "Class distribution after filtering:\n", "label\n", "1    0.552717\n", "0    0.447283\n", "Name: proportion, dtype: float64\n", "Dataset processing completed successfully!\n"]}], "source": ["# Part 4: Process and Combine Datasets with Filtering\n", "def process_and_combine_datasets():\n", "    \"\"\"\n", "    Load, process, combine, and filter the fake and true news datasets.\n", "    \"\"\"\n", "    import os\n", "    \n", "    # Get the current working directory\n", "    current_dir = os.getcwd()\n", "    print(f\"Current working directory: {current_dir}\")\n", "    \n", "    # Construct absolute paths\n", "    fake_path = os.path.join(current_dir, '..', '2_Data_Collection', 'data', 'Fake.csv')\n", "    true_path = os.path.join(current_dir, '..', '2_Data_Collection', 'data', 'True.csv')\n", "    \n", "    # Check if files exist\n", "    if not os.path.exists(fake_path):\n", "        print(f\"Error: Could not find fake news dataset at {fake_path}\")\n", "        return None\n", "    if not os.path.exists(true_path):\n", "        print(f\"Error: Could not find true news dataset at {true_path}\")\n", "        return None\n", "        \n", "    print(f\"Found datasets at:\\n{fake_path}\\n{true_path}\")\n", "    \n", "    print(\"Loading datasets...\")\n", "    try:\n", "        # Load datasets\n", "        fake_df = pd.read_csv(fake_path)\n", "        true_df = pd.read_csv(true_path)\n", "        \n", "        # Add labels (0 for fake, 1 for true)\n", "        fake_df['label'] = 0\n", "        true_df['label'] = 1\n", "        \n", "        print(\"Processing text data...\")\n", "        # Process text in both datasets\n", "        fake_df['text'] = fake_df['text'].apply(preprocess_text)\n", "        fake_df['title'] = fake_df['title'].apply(preprocess_text)\n", "        true_df['text'] = true_df['text'].apply(preprocess_text)\n", "        true_df['title'] = true_df['title'].apply(preprocess_text)\n", "        \n", "        # Combine datasets\n", "        news_df = pd.concat([fake_df, true_df], axis=0, ignore_index=True)\n", "        \n", "        # Add text length features\n", "        news_df['text_length'] = news_df['text'].apply(len)\n", "        news_df['title_length'] = news_df['title'].apply(len)\n", "        \n", "        print(\"Filtering dataset...\")\n", "        # Filter out entries with empty text or titles\n", "        news_df = news_df[news_df['text'].str.len() > 0]\n", "        news_df = news_df[news_df['title'].str.len() > 0]\n", "        \n", "        # Filter out extremely short texts (less than 50 characters)\n", "        news_df = news_df[news_df['text_length'] >= 50]\n", "        \n", "        # Filter out extremely long texts (above 99th percentile)\n", "        text_length_threshold = news_df['text_length'].quantile(0.99)\n", "        news_df = news_df[news_df['text_length'] <= text_length_threshold]\n", "        \n", "        # Remove duplicates\n", "        news_df = news_df.drop_duplicates(subset=['text', 'title'], keep='first')\n", "        \n", "        # Reset index after filtering\n", "        news_df = news_df.reset_index(drop=True)\n", "        \n", "        print(f\"Final dataset shape after filtering: {news_df.shape}\")\n", "        print(\"\\nClass distribution after filtering:\")\n", "        print(news_df['label'].value_counts(normalize=True))\n", "        \n", "        return news_df\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing datasets: {str(e)}\")\n", "        return None\n", "\n", "# Process and combine the datasets\n", "news_df = process_and_combine_datasets()\n", "\n", "# Check if processing was successful\n", "if news_df is not None:\n", "    print(\"Dataset processing completed successfully!\")\n", "else:\n", "    print(\"Dataset processing failed. Please check the error messages above.\")"]}, {"cell_type": "code", "execution_count": 22, "id": "d43e0529-d1a7-4f99-a1bd-5e18caf6e19d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/h5/xk9dvpn919jbdbwpxy8pyln40000gn/T/ipykernel_24715/2092519863.py:19: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `x` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.countplot(data=news_df, x='label', palette=['red', 'green'])\n", "/var/folders/h5/xk9dvpn919jbdbwpxy8pyln40000gn/T/ipykernel_24715/2092519863.py:26: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `x` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.boxplot(data=news_df, x='label', y='text_length', palette=['red', 'green'])\n", "/var/folders/h5/xk9dvpn919jbdbwpxy8pyln40000gn/T/ipykernel_24715/2092519863.py:33: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `x` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.boxplot(data=news_df, x='label', y='title_length', palette=['red', 'green'])\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Dataset Statistics:\n", "Total number of articles: 38043\n", "Number of fake articles: 17016\n", "Number of real articles: 21027\n", "\n", "Text Length Statistics by Label:\n", "\n", "Fake News (Label 0):\n", "        text_length  title_length\n", "count  17016.000000  17016.000000\n", "mean    1592.041490     70.152562\n", "std      886.870015     20.138728\n", "min       50.000000      8.000000\n", "25%     1102.000000     57.000000\n", "50%     1473.000000     67.000000\n", "75%     1959.000000     79.000000\n", "max     6353.000000    210.000000\n", "\n", "Real News (Label 1):\n", "        text_length  title_length\n", "count  21027.000000  21027.000000\n", "mean    1674.297142     54.090503\n", "std     1136.381252      8.832382\n", "min      102.000000     19.000000\n", "25%      652.000000     48.000000\n", "50%     1568.000000     54.000000\n", "75%     2275.000000     60.000000\n", "max     6322.000000    101.000000\n"]}], "source": ["# Part 5: Analysis and Visualization\n", "def analyze_and_visualize(news_df):\n", "    \"\"\"\n", "    Create visualizations and statistical analysis of the dataset.\n", "    \n", "    Parameters:\n", "    -----------\n", "    news_df : pandas.DataFrame\n", "        The preprocessed and filtered news DataFrame\n", "    \"\"\"\n", "    # Set style for better visualizations\n", "    plt.style.use('default')\n", "    \n", "    # Create a figure with multiple subplots\n", "    fig = plt.figure(figsize=(15, 12))\n", "    \n", "    # Plot 1: Distribution of labels (Fake vs Real)\n", "    plt.subplot(2, 2, 1)\n", "    sns.countplot(data=news_df, x='label', palette=['red', 'green'])\n", "    plt.title('Distribution of Fake vs Real News')\n", "    plt.xlabel('Label (0: Fake, 1: Real)')\n", "    plt.ylabel('Count')\n", "    \n", "    # Plot 2: Box plot of text lengths by label\n", "    plt.subplot(2, 2, 2)\n", "    sns.boxplot(data=news_df, x='label', y='text_length', palette=['red', 'green'])\n", "    plt.title('Text Length Distribution by Label')\n", "    plt.xlabel('Label (0: Fake, 1: Real)')\n", "    plt.ylabel('Text Length')\n", "    \n", "    # Plot 3: Box plot of title lengths by label\n", "    plt.subplot(2, 2, 3)\n", "    sns.boxplot(data=news_df, x='label', y='title_length', palette=['red', 'green'])\n", "    plt.title('Title Length Distribution by Label')\n", "    plt.xlabel('Label (0: Fake, 1: Real)')\n", "    plt.ylabel('Title Length')\n", "    \n", "    # Plot 4: Histogram of text lengths by label\n", "    plt.subplot(2, 2, 4)\n", "    sns.histplot(data=news_df, x='text_length', hue='label', bins=50, \n", "                alpha=0.5, palette=['red', 'green'])\n", "    plt.title('Text Length Distribution')\n", "    plt.xlabel('Text Length')\n", "    plt.ylabel('Count')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Print statistical summaries\n", "    print(\"\\nDataset Statistics:\")\n", "    print(f\"Total number of articles: {len(news_df)}\")\n", "    print(f\"Number of fake articles: {len(news_df[news_df['label'] == 0])}\")\n", "    print(f\"Number of real articles: {len(news_df[news_df['label'] == 1])}\")\n", "    \n", "    print(\"\\nText Length Statistics by Label:\")\n", "    print(\"\\nFake News (Label 0):\")\n", "    print(news_df[news_df['label'] == 0][['text_length', 'title_length']].describe())\n", "    print(\"\\nReal News (Label 1):\")\n", "    print(news_df[news_df['label'] == 1][['text_length', 'title_length']].describe())\n", "\n", "# Generate visualizations and statistics\n", "analyze_and_visualize(news_df)"]}, {"cell_type": "code", "execution_count": 23, "id": "31f7c6be-9043-44dd-a52c-7a99eda72247", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully saved filtered dataset to ../3_Data_Analysis/news.csv\n", "Successfully saved filtered dataset to news.csv\n", "\n", "Filtered Dataset Information:\n", "Total number of articles: 38043\n", "Number of features: 7\n", "\n", "Features in the dataset:\n", "- title\n", "- text\n", "- subject\n", "- date\n", "- label\n", "- text_length\n", "- title_length\n", "\n", "Class distribution in the filtered dataset:\n", "Fake news (0): 44.73%\n", "Real news (1): 55.27%\n", "\n", "Sample of the filtered dataset:\n", "                                               title  \\\n", "0  donald trump sends embarrassing new year eve m...   \n", "1  drunk bragging trump staffer started russian c...   \n", "2  sheriff david clarke becomes internet joke thr...   \n", "3  trump obsessed even o<PERSON>mas name coded website ...   \n", "4  pope <PERSON>ran<PERSON> called donald trump christmas speech   \n", "\n", "                                                text subject  \\\n", "0  donald trump wish american happy new year leav...    News   \n", "1  house intelligence committee chairman devin nu...    News   \n", "2  friday revealed former milwaukee sheriff david...    News   \n", "3  christmas day donald trump announced would bac...    News   \n", "4  pope <PERSON>ran<PERSON> used annual christmas day message...    News   \n", "\n", "                date  label  text_length  title_length  \n", "0  December 31, 2017      0         1638            63  \n", "1  December 31, 2017      0         1367            68  \n", "2  December 30, 2017      0         2177            70  \n", "3  December 29, 2017      0         1703            51  \n", "4  December 25, 2017      0         1426            49  \n"]}], "source": ["# Part 6: Save the Filtered Dataset\n", "def save_filtered_dataset(news_df):\n", "    \"\"\"\n", "    Save the filtered dataset to both the analysis folder and current directory.\n", "    \n", "    Parameters:\n", "    -----------\n", "    news_df : pandas.DataFrame\n", "        The preprocessed and filtered news DataFrame\n", "    \"\"\"\n", "    try:\n", "        # Save in 3_Data_Analysis folder\n", "        analysis_path = '../3_Data_Analysis/news.csv'\n", "        os.makedirs(os.path.dirname(analysis_path), exist_ok=True)\n", "        news_df.to_csv(analysis_path, index=False)\n", "        print(f\"Successfully saved filtered dataset to {analysis_path}\")\n", "        \n", "        # Save in current directory\n", "        current_path = 'news.csv'\n", "        news_df.to_csv(current_path, index=False)\n", "        print(f\"Successfully saved filtered dataset to {current_path}\")\n", "        \n", "        # Print dataset information\n", "        print(\"\\nFiltered Dataset Information:\")\n", "        print(f\"Total number of articles: {len(news_df)}\")\n", "        print(f\"Number of features: {len(news_df.columns)}\")\n", "        print(\"\\nFeatures in the dataset:\")\n", "        for col in news_df.columns:\n", "            print(f\"- {col}\")\n", "        \n", "        print(\"\\nClass distribution in the filtered dataset:\")\n", "        class_dist = news_df['label'].value_counts(normalize=True)\n", "        print(f\"Fake news (0): {class_dist[0]:.2%}\")\n", "        print(f\"Real news (1): {class_dist[1]:.2%}\")\n", "        \n", "        print(\"\\nSample of the filtered dataset:\")\n", "        print(news_df.head())\n", "        \n", "    except Exception as e:\n", "        print(f\"Error saving the dataset: {str(e)}\")\n", "\n", "# Save the filtered dataset\n", "save_filtered_dataset(news_df)"]}, {"cell_type": "code", "execution_count": 25, "id": "7bb72443-35a4-4d0e-9cf0-aca1030ad6e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully created requirements.txt\n"]}], "source": ["def create_requirements():\n", "    \"\"\"\n", "    Create a requirements.txt file with necessary packages for the project.\n", "    \"\"\"\n", "    requirements = \"\"\"\n", "pandas>=1.5.0\n", "numpy>=1.21.0\n", "nltk>=3.6.0\n", "scikit-learn>=1.0.0\n", "textblob>=0.15.3\n", "matplotlib>=3.4.0\n", "seaborn>=0.11.0\n", "jupyter>=1.0.0\n", "\"\"\"\n", "    \n", "    try:\n", "        with open('requirements.txt', 'w') as f:\n", "            f.write(requirements.strip())\n", "        print(\"Successfully created requirements.txt\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error creating requirements.txt: {str(e)}\")\n", "\n", "# Create the requirements file\n", "create_requirements()"]}, {"cell_type": "code", "execution_count": 26, "id": "b12e31e4-24d2-4145-b538-589199c60d08", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Text Complexity Analysis for first article:\n", "avg_word_length: 5.69\n", "sentence_count: 1\n", "avg_sentence_length: 245.0\n"]}], "source": ["# Part 7: Text Complexity Analysis\n", "def analyze_text_complexity(text, title):\n", "    \"\"\"\n", "    Analyze text complexity using basic metrics.\n", "    \n", "    Parameters:\n", "    -----------\n", "    text : str\n", "        The main text content\n", "    title : str\n", "        The title of the text\n", "        \n", "    Returns:\n", "    --------\n", "    dict\n", "        Dictionary containing complexity metrics\n", "    \"\"\"\n", "    # 1. Average word length\n", "    words = text.split()\n", "    avg_word_length = sum(len(word) for word in words) / len(words) if words else 0\n", "    \n", "    # 2. Sentence count (splitting by ., !, ?)\n", "    sentences = [s.strip() for s in text.replace('!', '.').replace('?', '.').split('.') if s.strip()]\n", "    sentence_count = len(sentences)\n", "    \n", "    # 3. Average sentence length\n", "    avg_sentence_length = len(words) / sentence_count if sentence_count > 0 else 0\n", "    \n", "    return {\n", "        'avg_word_length': round(avg_word_length, 2),\n", "        'sentence_count': sentence_count,\n", "        'avg_sentence_length': round(avg_sentence_length, 2)\n", "    }\n", "\n", "# Example usage:\n", "# Let's analyze one article first\n", "sample_text = news_df.iloc[0]['text']\n", "sample_title = news_df.iloc[0]['title']\n", "complexity_metrics = analyze_text_complexity(sample_text, sample_title)\n", "print(\"\\nText Complexity Analysis for first article:\")\n", "for metric, value in complexity_metrics.items():\n", "    print(f\"{metric}: {value}\")"]}, {"cell_type": "code", "execution_count": 27, "id": "c392083e-a916-4dd5-badf-e9ffd6d68b68", "metadata": {}, "outputs": [], "source": ["# Part 8: Stylometric Feature Analysis\n", "def analyze_stylometric_features(text):\n", "    \"\"\"\n", "    Extract stylometric features from text.\n", "    \n", "    Parameters:\n", "    -----------\n", "    text : str\n", "        The text to analyze\n", "        \n", "    Returns:\n", "    --------\n", "    dict\n", "        Dictionary containing stylometric features\n", "    \"\"\"\n", "    # 1. Punctuation patterns\n", "    exclamation_count = text.count('!')\n", "    question_count = text.count('?')\n", "    quotes_count = text.count('\"') + text.count(\"'\")\n", "    \n", "    # 2. Capitalization patterns\n", "    words = text.split()\n", "    capitalized_words = sum(1 for word in words if word and word[0].isupper())\n", "    all_caps_words = sum(1 for word in words if word.isupper() and len(word) > 1)\n", "    \n", "    # 3. Special character patterns\n", "    special_chars = sum(1 for char in text if not char.isalnum() and not char.isspace())\n", "    \n", "    return {\n", "        'exclamation_density': exclamation_count / len(words) if words else 0,\n", "        'question_density': question_count / len(words) if words else 0,\n", "        'quotes_density': quotes_count / len(words) if words else 0,\n", "        'capitalized_ratio': capitalized_words / len(words) if words else 0,\n", "        'all_caps_ratio': all_caps_words / len(words) if words else 0,\n", "        'special_chars_density': special_chars / len(words) if words else 0\n", "    }"]}, {"cell_type": "code", "execution_count": 28, "id": "d1509f52-8c34-44b8-a053-fbf780d514ee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset shape: (38043, 7)\n", "\n", "Testing with first article:\n", "\n", "Stylometric features for first article:\n", "exclamation_density: 0.0\n", "question_density: 0.0\n", "quotes_density: 0.0\n", "capitalized_ratio: 0.0\n", "all_caps_ratio: 0.0\n", "special_chars_density: 0.0\n", "\n", "Applying to full dataset...\n", "Error: Please make sure the dataset (news_df) is loaded first!\n", "You need to run the previous data loading and preprocessing steps before this analysis.\n"]}], "source": ["# Test the stylometric analysis\n", "# First, let's make sure we have our dataset loaded\n", "try:\n", "    # Check if news_df exists and has content\n", "    print(\"Dataset shape:\", news_df.shape)\n", "    \n", "    # Test with a single article first\n", "    print(\"\\nTesting with first article:\")\n", "    sample_text = news_df.iloc[0]['text']\n", "    sample_features = analyze_stylometric_features(sample_text)\n", "    print(\"\\nStylometric features for first article:\")\n", "    for feature, value in sample_features.items():\n", "        print(f\"{feature}: {value}\")\n", "    \n", "    # Now apply to full dataset\n", "    print(\"\\nApplying to full dataset...\")\n", "    news_df = add_stylometric_features(news_df)\n", "    \n", "    # Show summary of new features\n", "    print(\"\\nSummary statistics of new features:\")\n", "    print(news_df[['exclamation_density', 'question_density', 'quotes_density', \n", "                   'capitalized_ratio', 'all_caps_ratio', 'special_chars_density']].describe())\n", "    \n", "    # Save the updated dataset\n", "    news_df.to_csv('news_with_stylometric.csv', index=False)\n", "    print(\"\\nUpdated dataset saved as 'news_with_stylometric.csv'\")\n", "    \n", "except NameError:\n", "    print(\"Error: Please make sure the dataset (news_df) is loaded first!\")\n", "    print(\"You need to run the previous data loading and preprocessing steps before this analysis.\")\n", "except Exception as e:\n", "    print(f\"An error occurred: {str(e)}\")"]}, {"cell_type": "code", "execution_count": 29, "id": "0af14b17-8f56-44df-9db0-84c25fca515e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initial dataset shape: (38043, 7)\n", "\n", "Columns in dataset: ['title', 'text', 'subject', 'date', 'label', 'text_length', 'title_length']\n", "\n", "First few rows:\n", "                                               title  \\\n", "0  donald trump sends embarrassing new year eve m...   \n", "1  drunk bragging trump staffer started russian c...   \n", "\n", "                                                text subject  \\\n", "0  donald trump wish american happy new year leav...    News   \n", "1  house intelligence committee chairman devin nu...    News   \n", "\n", "                date  label  text_length  title_length  \n", "0  December 31, 2017      0         1638            63  \n", "1  December 31, 2017      0         1367            68  \n"]}], "source": ["# Load and verify the dataset\n", "import pandas as pd\n", "\n", "# Load the dataset from your previous saved file\n", "news_df = pd.read_csv('news.csv')\n", "print(\"Initial dataset shape:\", news_df.shape)\n", "print(\"\\nColumns in dataset:\", news_df.columns.tolist())\n", "print(\"\\nFirst few rows:\")\n", "print(news_df.head(2))"]}, {"cell_type": "code", "execution_count": 30, "id": "7bd3263a-8aaa-4a39-88b1-91ea46def880", "metadata": {}, "outputs": [], "source": ["def analyze_stylometric_features(text):\n", "    # ... (your existing function code)\n", "    pass\n", "\n", "def add_stylometric_features(df):\n", "    # ... (your existing function code)\n", "    pass"]}, {"cell_type": "code", "execution_count": 31, "id": "d6c588c0-a9f9-4b48-bca3-82336cbd4437", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting stylometric analysis...\n", "\n", "Stylometric features for first article:\n"]}, {"ename": "AttributeError", "evalue": "'NoneType' object has no attribute 'items'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[31]\u001b[39m\u001b[32m, line 8\u001b[39m\n\u001b[32m      6\u001b[39m sample_features = analyze_stylometric_features(sample_text)\n\u001b[32m      7\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33mStylometric features for first article:\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m feature, value \u001b[38;5;129;01min\u001b[39;00m \u001b[43msample_features\u001b[49m\u001b[43m.\u001b[49m\u001b[43mitems\u001b[49m():\n\u001b[32m      9\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfeature\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mvalue\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m     11\u001b[39m \u001b[38;5;66;03m# Apply to full dataset\u001b[39;00m\n", "\u001b[31mAttributeError\u001b[39m: 'NoneType' object has no attribute 'items'"]}], "source": ["# Apply stylometric analysis\n", "print(\"Starting stylometric analysis...\")\n", "\n", "# Test with first article\n", "sample_text = news_df.iloc[0]['text']\n", "sample_features = analyze_stylometric_features(sample_text)\n", "print(\"\\nStylometric features for first article:\")\n", "for feature, value in sample_features.items():\n", "    print(f\"{feature}: {value}\")\n", "\n", "# Apply to full dataset\n", "print(\"\\nApplying to full dataset...\")\n", "news_df = add_stylometric_features(news_df)\n", "\n", "# Save the updated dataset\n", "news_df.to_csv('news_with_stylometric.csv', index=False)\n", "print(\"\\nUpdated dataset saved as 'news_with_stylometric.csv'\")\n", "\n", "# Show the new columns\n", "print(\"\\nNew columns added:\", \n", "      [col for col in news_df.columns if col not in ['text', 'title', 'label']])"]}, {"cell_type": "code", "execution_count": 32, "id": "91987e10-4e7d-4197-9a6d-04b73e202b2f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting stylometric analysis...\n", "\n", "Stylometric features for first article:\n", "exclamation_density: 0.0\n", "question_density: 0.0\n", "quotes_density: 0.0\n", "capitalized_ratio: 0.0\n", "all_caps_ratio: 0.0\n", "special_chars_density: 0.0\n", "\n", "Applying to full dataset...\n", "Adding stylometric features...\n", "\n", "Stylometric features added successfully!\n", "\n", "Feature statistics:\n", "       exclamation_density  question_density  quotes_density  \\\n", "count              38043.0           38043.0         38043.0   \n", "mean                   0.0               0.0             0.0   \n", "std                    0.0               0.0             0.0   \n", "min                    0.0               0.0             0.0   \n", "25%                    0.0               0.0             0.0   \n", "50%                    0.0               0.0             0.0   \n", "75%                    0.0               0.0             0.0   \n", "max                    0.0               0.0             0.0   \n", "\n", "       capitalized_ratio  all_caps_ratio  special_chars_density  \n", "count            38043.0         38043.0           38043.000000  \n", "mean                 0.0             0.0               0.000196  \n", "std                  0.0             0.0               0.007774  \n", "min                  0.0             0.0               0.000000  \n", "25%                  0.0             0.0               0.000000  \n", "50%                  0.0             0.0               0.000000  \n", "75%                  0.0             0.0               0.000000  \n", "max                  0.0             0.0               1.075000  \n", "\n", "Updated dataset saved as 'news_with_stylometric.csv'\n"]}], "source": ["def analyze_stylometric_features(text):\n", "    \"\"\"\n", "    Extract stylometric features from text.\n", "    \n", "    Parameters:\n", "    -----------\n", "    text : str\n", "        The text to analyze\n", "        \n", "    Returns:\n", "    --------\n", "    dict\n", "        Dictionary containing stylometric features\n", "    \"\"\"\n", "    # Ensure text is a string\n", "    text = str(text)\n", "    \n", "    # 1. Punctuation patterns\n", "    exclamation_count = text.count('!')\n", "    question_count = text.count('?')\n", "    quotes_count = text.count('\"') + text.count(\"'\")\n", "    \n", "    # 2. Capitalization patterns\n", "    words = text.split()\n", "    total_words = len(words) if words else 1  # Avoid division by zero\n", "    capitalized_words = sum(1 for word in words if word and word[0].isupper())\n", "    all_caps_words = sum(1 for word in words if word.isupper() and len(word) > 1)\n", "    \n", "    # 3. Special character patterns\n", "    special_chars = sum(1 for char in text if not char.isalnum() and not char.isspace())\n", "    \n", "    return {\n", "        'exclamation_density': round(exclamation_count / total_words, 4),\n", "        'question_density': round(question_count / total_words, 4),\n", "        'quotes_density': round(quotes_count / total_words, 4),\n", "        'capitalized_ratio': round(capitalized_words / total_words, 4),\n", "        'all_caps_ratio': round(all_caps_words / total_words, 4),\n", "        'special_chars_density': round(special_chars / total_words, 4)\n", "    }\n", "\n", "def add_stylometric_features(df):\n", "    \"\"\"\n", "    Add stylometric features to the dataframe\n", "    \"\"\"\n", "    print(\"Adding stylometric features...\")\n", "    \n", "    # Apply stylometric analysis to each text\n", "    features = df['text'].apply(analyze_stylometric_features)\n", "    \n", "    # Convert the dictionary of features into separate columns\n", "    for feature in ['exclamation_density', 'question_density', 'quotes_density', \n", "                   'capitalized_ratio', 'all_caps_ratio', 'special_chars_density']:\n", "        df[feature] = features.apply(lambda x: x[feature])\n", "    \n", "    print(\"\\nStylometric features added successfully!\")\n", "    print(\"\\nFeature statistics:\")\n", "    print(df[['exclamation_density', 'question_density', 'quotes_density', \n", "              'capitalized_ratio', 'all_caps_ratio', 'special_chars_density']].describe())\n", "    return df\n", "\n", "# Now test the implementation\n", "print(\"Starting stylometric analysis...\")\n", "\n", "# Test with first article\n", "sample_text = news_df.iloc[0]['text']\n", "sample_features = analyze_stylometric_features(sample_text)\n", "print(\"\\nStylometric features for first article:\")\n", "for feature, value in sample_features.items():\n", "    print(f\"{feature}: {value}\")\n", "\n", "# Apply to full dataset\n", "print(\"\\nApplying to full dataset...\")\n", "news_df = add_stylometric_features(news_df)\n", "\n", "# Save the updated dataset\n", "news_df.to_csv('news_with_stylometric.csv', index=False)\n", "print(\"\\nUpdated dataset saved as 'news_with_stylometric.csv'\")"]}, {"cell_type": "code", "execution_count": 33, "id": "a9a827e8-f93c-46b6-ab1b-0e9e9118f764", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding enhanced stylometric features...\n", "Analyzing sentence structure...\n", "\n", "Enhanced features added successfully!\n", "\n", "New feature statistics:\n", "       avg_sentence_length  sentence_length_std  num_sentences\n", "count         38043.000000              38043.0        38043.0\n", "mean            221.314670                  0.0            1.0\n", "std             138.886993                  0.0            0.0\n", "min               5.000000                  0.0            1.0\n", "25%             125.000000                  0.0            1.0\n", "50%             205.000000                  0.0            1.0\n", "75%             283.000000                  0.0            1.0\n", "max             895.000000                  0.0            1.0\n", "\n", "Updated dataset saved as 'news_with_enhanced_stylometric.csv'\n"]}], "source": ["def enhance_stylometric_features(df):\n", "    \"\"\"\n", "    Add additional stylometric features focusing on sentence structure\n", "    \"\"\"\n", "    print(\"Adding enhanced stylometric features...\")\n", "    \n", "    def analyze_sentence_structure(text):\n", "        sentences = text.split('.')\n", "        # Remove empty strings from sentence splitting\n", "        sentences = [s.strip() for s in sentences if s.strip()]\n", "        \n", "        # Average sentence length (in words)\n", "        sentence_lengths = [len(s.split()) for s in sentences]\n", "        avg_sentence_length = sum(sentence_lengths) / len(sentences) if sentences else 0\n", "        \n", "        # Sentence length variation\n", "        sentence_length_std = np.std(sentence_lengths) if len(sentences) > 1 else 0\n", "        \n", "        return {\n", "            'avg_sentence_length': round(avg_sentence_length, 2),\n", "            'sentence_length_std': round(sentence_length_std, 2),\n", "            'num_sentences': len(sentences)\n", "        }\n", "    \n", "    # Apply sentence structure analysis\n", "    print(\"Analyzing sentence structure...\")\n", "    sentence_features = df['text'].apply(analyze_sentence_structure)\n", "    \n", "    # Add new features to dataframe\n", "    for feature in ['avg_sentence_length', 'sentence_length_std', 'num_sentences']:\n", "        df[feature] = sentence_features.apply(lambda x: x[feature])\n", "    \n", "    print(\"\\nEnhanced features added successfully!\")\n", "    print(\"\\nNew feature statistics:\")\n", "    print(df[['avg_sentence_length', 'sentence_length_std', 'num_sentences']].describe())\n", "    return df\n", "\n", "# Add the enhanced features\n", "news_df = enhance_stylometric_features(news_df)\n", "\n", "# Save the updated dataset\n", "news_df.to_csv('news_with_enhanced_stylometric.csv', index=False)\n", "print(\"\\nUpdated dataset saved as 'news_with_enhanced_stylometric.csv'\")"]}, {"cell_type": "code", "execution_count": 34, "id": "ccc909b8-9f25-40af-b30e-430f04ee4fe0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding sentiment analysis features...\n", "Analyzing sentiment for all articles...\n"]}, {"ename": "MissingCorpusE<PERSON>r", "evalue": "\nLooks like you are missing some required data for this feature.\n\nTo download the necessary data, simply run\n\n    python -m textblob.download_corpora\n\nor use the NLTK downloader to download the missing data: http://nltk.org/data.html\nIf this doesn't fix the problem, file an issue at https://github.com/sloria/TextBlob/issues.\n", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                               <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/textblob/decorators.py:35\u001b[39m, in \u001b[36mrequires_nltk_corpus.<locals>.decorated\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m     34\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m35\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     36\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mLookupError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m error:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/textblob/tokenizers.py:60\u001b[39m, in \u001b[36mSentenceTokenizer.tokenize\u001b[39m\u001b[34m(self, text)\u001b[39m\n\u001b[32m     59\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Return a list of sentences.\"\"\"\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m60\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mnltk\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtokenize\u001b[49m\u001b[43m.\u001b[49m\u001b[43msent_tokenize\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtext\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/nltk/tokenize/__init__.py:119\u001b[39m, in \u001b[36msent_tokenize\u001b[39m\u001b[34m(text, language)\u001b[39m\n\u001b[32m    110\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    111\u001b[39m \u001b[33;03mReturn a sentence-tokenized copy of *text*,\u001b[39;00m\n\u001b[32m    112\u001b[39m \u001b[33;03musing NLTK's recommended sentence tokenizer\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    117\u001b[39m \u001b[33;03m:param language: the model name in the Punkt corpus\u001b[39;00m\n\u001b[32m    118\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m119\u001b[39m tokenizer = \u001b[43m_get_punkt_tokenizer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlanguage\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    120\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m tokenizer.tokenize(text)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/nltk/tokenize/__init__.py:105\u001b[39m, in \u001b[36m_get_punkt_tokenizer\u001b[39m\u001b[34m(language)\u001b[39m\n\u001b[32m     98\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m     99\u001b[39m \u001b[33;03mA constructor for the PunktTokenizer that utilizes\u001b[39;00m\n\u001b[32m    100\u001b[39m \u001b[33;03ma lru cache for performance.\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    103\u001b[39m \u001b[33;03m:type language: str\u001b[39;00m\n\u001b[32m    104\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m105\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mPunktTokenizer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlanguage\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/nltk/tokenize/punkt.py:1744\u001b[39m, in \u001b[36mPunktTokenizer.__init__\u001b[39m\u001b[34m(self, lang)\u001b[39m\n\u001b[32m   1743\u001b[39m PunktSentenceTokenizer.\u001b[34m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m1744\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mload_lang\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlang\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/nltk/tokenize/punkt.py:1749\u001b[39m, in \u001b[36mPunktTokenizer.load_lang\u001b[39m\u001b[34m(self, lang)\u001b[39m\n\u001b[32m   1747\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnltk\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdata\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m find\n\u001b[32m-> \u001b[39m\u001b[32m1749\u001b[39m lang_dir = \u001b[43mfind\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43mf\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtokenizers/punkt_tab/\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mlang\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m/\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m   1750\u001b[39m \u001b[38;5;28mself\u001b[39m._params = load_punkt_params(lang_dir)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/nltk/data.py:579\u001b[39m, in \u001b[36mfind\u001b[39m\u001b[34m(resource_name, paths)\u001b[39m\n\u001b[32m    578\u001b[39m resource_not_found = \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00msep\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00mmsg\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00msep\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m--> \u001b[39m\u001b[32m579\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mLookupError\u001b[39;00m(resource_not_found)\n", "\u001b[31mLookupError\u001b[39m: \n**********************************************************************\n  Resource \u001b[93mpunkt_tab\u001b[0m not found.\n  Please use the NLTK Downloader to obtain the resource:\n\n  \u001b[31m>>> import nltk\n  >>> nltk.download('punkt_tab')\n  \u001b[0m\n  For more information see: https://www.nltk.org/data.html\n\n  Attempted to load \u001b[93mtokenizers/punkt_tab/english/\u001b[0m\n\n  Searched in:\n    - '/Users/<USER>/nltk_data'\n    - '/Users/<USER>/Documents/FakeNewsDetection_Development/venv/nltk_data'\n    - '/Users/<USER>/Documents/FakeNewsDetection_Development/venv/share/nltk_data'\n    - '/Users/<USER>/Documents/FakeNewsDetection_Development/venv/lib/nltk_data'\n    - '/usr/share/nltk_data'\n    - '/usr/local/share/nltk_data'\n    - '/usr/lib/nltk_data'\n    - '/usr/local/lib/nltk_data'\n**********************************************************************\n", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mMissingCorpusError\u001b[39m                        <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[34]\u001b[39m\u001b[32m, line 55\u001b[39m\n\u001b[32m     52\u001b[39m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m df\n\u001b[32m     54\u001b[39m \u001b[38;5;66;03m# Add sentiment features\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m55\u001b[39m news_df = \u001b[43manalyze_sentiment\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnews_df\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     57\u001b[39m \u001b[38;5;66;03m# Save the updated dataset\u001b[39;00m\n\u001b[32m     58\u001b[39m news_df.to_csv(\u001b[33m'\u001b[39m\u001b[33mnews_with_sentiment.csv\u001b[39m\u001b[33m'\u001b[39m, index=\u001b[38;5;28;01mFalse\u001b[39;00m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[34]\u001b[39m\u001b[32m, line 33\u001b[39m, in \u001b[36manalyze_sentiment\u001b[39m\u001b[34m(df)\u001b[39m\n\u001b[32m     31\u001b[39m \u001b[38;5;66;03m# Apply sentiment analysis\u001b[39;00m\n\u001b[32m     32\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mAnalyzing sentiment for all articles...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m33\u001b[39m sentiment_features = \u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mtext\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m.\u001b[49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[43mget_sentiment_features\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     35\u001b[39m \u001b[38;5;66;03m# Add features to dataframe\u001b[39;00m\n\u001b[32m     36\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m feature \u001b[38;5;129;01min\u001b[39;00m [\u001b[33m'\u001b[39m\u001b[33mpolarity\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33msubjectivity\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33msentiment_std\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mmax_polarity\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mmin_polarity\u001b[39m\u001b[33m'\u001b[39m]:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/pandas/core/series.py:4924\u001b[39m, in \u001b[36mSeries.apply\u001b[39m\u001b[34m(self, func, convert_dtype, args, by_row, **kwargs)\u001b[39m\n\u001b[32m   4789\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mapply\u001b[39m(\n\u001b[32m   4790\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   4791\u001b[39m     func: AggFuncType,\n\u001b[32m   (...)\u001b[39m\u001b[32m   4796\u001b[39m     **kwargs,\n\u001b[32m   4797\u001b[39m ) -> DataFrame | Series:\n\u001b[32m   4798\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m   4799\u001b[39m \u001b[33;03m    Invoke function on values of Series.\u001b[39;00m\n\u001b[32m   4800\u001b[39m \n\u001b[32m   (...)\u001b[39m\u001b[32m   4915\u001b[39m \u001b[33;03m    dtype: float64\u001b[39;00m\n\u001b[32m   4916\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m   4917\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mSeriesApply\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   4918\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   4919\u001b[39m \u001b[43m        \u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4920\u001b[39m \u001b[43m        \u001b[49m\u001b[43mconvert_dtype\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconvert_dtype\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4921\u001b[39m \u001b[43m        \u001b[49m\u001b[43mby_row\u001b[49m\u001b[43m=\u001b[49m\u001b[43mby_row\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4922\u001b[39m \u001b[43m        \u001b[49m\u001b[43margs\u001b[49m\u001b[43m=\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4923\u001b[39m \u001b[43m        \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m-> \u001b[39m\u001b[32m4924\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/pandas/core/apply.py:1427\u001b[39m, in \u001b[36mSeriesApply.apply\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1424\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.apply_compat()\n\u001b[32m   1426\u001b[39m \u001b[38;5;66;03m# self.func is Callable\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1427\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mapply_standard\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/pandas/core/apply.py:1507\u001b[39m, in \u001b[36mSeriesApply.apply_standard\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1501\u001b[39m \u001b[38;5;66;03m# row-wise access\u001b[39;00m\n\u001b[32m   1502\u001b[39m \u001b[38;5;66;03m# apply doesn't have a `na_action` keyword and for backward compat reasons\u001b[39;00m\n\u001b[32m   1503\u001b[39m \u001b[38;5;66;03m# we need to give `na_action=\"ignore\"` for categorical data.\u001b[39;00m\n\u001b[32m   1504\u001b[39m \u001b[38;5;66;03m# TODO: remove the `na_action=\"ignore\"` when that default has been changed in\u001b[39;00m\n\u001b[32m   1505\u001b[39m \u001b[38;5;66;03m#  Categorical (GH51645).\u001b[39;00m\n\u001b[32m   1506\u001b[39m action = \u001b[33m\"\u001b[39m\u001b[33mignore\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(obj.dtype, CategoricalDtype) \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1507\u001b[39m mapped = \u001b[43mobj\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_map_values\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1508\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmapper\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcurried\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mna_action\u001b[49m\u001b[43m=\u001b[49m\u001b[43maction\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconvert\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mconvert_dtype\u001b[49m\n\u001b[32m   1509\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1511\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(mapped) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(mapped[\u001b[32m0\u001b[39m], ABCSeries):\n\u001b[32m   1512\u001b[39m     \u001b[38;5;66;03m# GH#43986 Need to do list(mapped) in order to get treated as nested\u001b[39;00m\n\u001b[32m   1513\u001b[39m     \u001b[38;5;66;03m#  See also GH#25959 regarding EA support\u001b[39;00m\n\u001b[32m   1514\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m obj._constructor_expanddim(\u001b[38;5;28mlist\u001b[39m(mapped), index=obj.index)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/pandas/core/base.py:921\u001b[39m, in \u001b[36mIndexOpsMixin._map_values\u001b[39m\u001b[34m(self, mapper, na_action, convert)\u001b[39m\n\u001b[32m    918\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(arr, ExtensionArray):\n\u001b[32m    919\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m arr.map(mapper, na_action=na_action)\n\u001b[32m--> \u001b[39m\u001b[32m921\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43malgorithms\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmap_array\u001b[49m\u001b[43m(\u001b[49m\u001b[43marr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmapper\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mna_action\u001b[49m\u001b[43m=\u001b[49m\u001b[43mna_action\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconvert\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconvert\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/pandas/core/algorithms.py:1743\u001b[39m, in \u001b[36mmap_array\u001b[39m\u001b[34m(arr, mapper, na_action, convert)\u001b[39m\n\u001b[32m   1741\u001b[39m values = arr.astype(\u001b[38;5;28mobject\u001b[39m, copy=\u001b[38;5;28;01mF<PERSON>e\u001b[39;00m)\n\u001b[32m   1742\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m na_action \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1743\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mlib\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmap_infer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmapper\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconvert\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconvert\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1744\u001b[39m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[32m   1745\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m lib.map_infer_mask(\n\u001b[32m   1746\u001b[39m         values, mapper, mask=isna(values).view(np.uint8), convert=convert\n\u001b[32m   1747\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32mlib.pyx:2972\u001b[39m, in \u001b[36mpandas._libs.lib.map_infer\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[34]\u001b[39m\u001b[32m, line 21\u001b[39m, in \u001b[36manalyze_sentiment.<locals>.get_sentiment_features\u001b[39m\u001b[34m(text)\u001b[39m\n\u001b[32m     18\u001b[39m subjectivity = blob.sentiment.subjectivity\n\u001b[32m     20\u001b[39m \u001b[38;5;66;03m# Get sentence-level sentiment stats\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m21\u001b[39m sentence_polarities = [sentence.sentiment.polarity \u001b[38;5;28;01mfor\u001b[39;00m sentence \u001b[38;5;129;01min\u001b[39;00m \u001b[43mblob\u001b[49m\u001b[43m.\u001b[49m\u001b[43msentences\u001b[49m]\n\u001b[32m     23\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m {\n\u001b[32m     24\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mpolarity\u001b[39m\u001b[33m'\u001b[39m: \u001b[38;5;28mround\u001b[39m(polarity, \u001b[32m3\u001b[39m),\n\u001b[32m     25\u001b[39m     \u001b[33m'\u001b[39m\u001b[33msubjectivity\u001b[39m\u001b[33m'\u001b[39m: \u001b[38;5;28mround\u001b[39m(subjectivity, \u001b[32m3\u001b[39m),\n\u001b[32m   (...)\u001b[39m\u001b[32m     28\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mmin_polarity\u001b[39m\u001b[33m'\u001b[39m: \u001b[38;5;28mround\u001b[39m(\u001b[38;5;28mmin\u001b[39m(sentence_polarities) \u001b[38;5;28;01mif\u001b[39;00m sentence_polarities \u001b[38;5;28;01melse\u001b[39;00m \u001b[32m0\u001b[39m, \u001b[32m3\u001b[39m)\n\u001b[32m     29\u001b[39m }\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/textblob/decorators.py:23\u001b[39m, in \u001b[36mcached_property.__get__\u001b[39m\u001b[34m(self, obj, cls)\u001b[39m\n\u001b[32m     21\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m obj \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m     22\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\n\u001b[32m---> \u001b[39m\u001b[32m23\u001b[39m value = obj.\u001b[34m__dict__\u001b[39m[\u001b[38;5;28mself\u001b[39m.func.\u001b[34m__name__\u001b[39m] = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mobj\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     24\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m value\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/textblob/blob.py:615\u001b[39m, in \u001b[36mTextBlob.sentences\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    612\u001b[39m \u001b[38;5;129m@cached_property\u001b[39m\n\u001b[32m    613\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34msentences\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m    614\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Return list of :class:`Sentence <Sentence>` objects.\"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m615\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_create_sentence_objects\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/textblob/blob.py:658\u001b[39m, in \u001b[36mTextBlob._create_sentence_objects\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    656\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Returns a list of Sentence objects from the raw text.\"\"\"\u001b[39;00m\n\u001b[32m    657\u001b[39m sentence_objects = []\n\u001b[32m--> \u001b[39m\u001b[32m658\u001b[39m sentences = \u001b[43msent_tokenize\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mraw\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    659\u001b[39m char_index = \u001b[32m0\u001b[39m  \u001b[38;5;66;03m# Keeps track of character index within the blob\u001b[39;00m\n\u001b[32m    660\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m sent \u001b[38;5;129;01min\u001b[39;00m sentences:\n\u001b[32m    661\u001b[39m     \u001b[38;5;66;03m# Compute the start and end indices of the sentence\u001b[39;00m\n\u001b[32m    662\u001b[39m     \u001b[38;5;66;03m# within the blob\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/textblob/base.py:68\u001b[39m, in \u001b[36mBaseTokenizer.itokenize\u001b[39m\u001b[34m(self, text, *args, **kwargs)\u001b[39m\n\u001b[32m     61\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mitokenize\u001b[39m(\u001b[38;5;28mself\u001b[39m, text, *args, **kwargs):\n\u001b[32m     62\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Return a generator that generates tokens \"on-demand\".\u001b[39;00m\n\u001b[32m     63\u001b[39m \n\u001b[32m     64\u001b[39m \u001b[33;03m    .. versionadded:: 0.6.0\u001b[39;00m\n\u001b[32m     65\u001b[39m \n\u001b[32m     66\u001b[39m \u001b[33;03m    :rtype: generator\u001b[39;00m\n\u001b[32m     67\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m68\u001b[39m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m (t \u001b[38;5;28;01mfor\u001b[39;00m t \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mtokenize\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtext\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/textblob/decorators.py:37\u001b[39m, in \u001b[36mrequires_nltk_corpus.<locals>.decorated\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m     35\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m func(*args, **kwargs)\n\u001b[32m     36\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mLookupError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m error:\n\u001b[32m---> \u001b[39m\u001b[32m37\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m <PERSON>CorpusError() \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01<PERSON><PERSON><PERSON>\u001b[39;00m\n", "\u001b[31mMissingCorpusError\u001b[39m: \nLooks like you are missing some required data for this feature.\n\nTo download the necessary data, simply run\n\n    python -m textblob.download_corpora\n\nor use the NLTK downloader to download the missing data: http://nltk.org/data.html\nIf this doesn't fix the problem, file an issue at https://github.com/sloria/TextBlob/issues.\n"]}], "source": ["from textblob import TextBlob\n", "import numpy as np\n", "\n", "def analyze_sentiment(df):\n", "    \"\"\"\n", "    Add sentiment analysis features to the dataset\n", "    \"\"\"\n", "    print(\"Adding sentiment analysis features...\")\n", "    \n", "    def get_sentiment_features(text):\n", "        # Create TextBlob object\n", "        blob = TextBlob(str(text))\n", "        \n", "        # Get the sentiment polarity (-1 to 1, negative to positive)\n", "        polarity = blob.sentiment.polarity\n", "        \n", "        # Get the subjectivity (0 to 1, objective to subjective)\n", "        subjectivity = blob.sentiment.subjectivity\n", "        \n", "        # Get sentence-level sentiment stats\n", "        sentence_polarities = [sentence.sentiment.polarity for sentence in blob.sentences]\n", "        \n", "        return {\n", "            'polarity': round(polarity, 3),\n", "            'subjectivity': round(subjectivity, 3),\n", "            'sentiment_std': round(np.std(sentence_polarities) if sentence_polarities else 0, 3),\n", "            'max_polarity': round(max(sentence_polarities) if sentence_polarities else 0, 3),\n", "            'min_polarity': round(min(sentence_polarities) if sentence_polarities else 0, 3)\n", "        }\n", "    \n", "    # Apply sentiment analysis\n", "    print(\"Analyzing sentiment for all articles...\")\n", "    sentiment_features = df['text'].apply(get_sentiment_features)\n", "    \n", "    # Add features to dataframe\n", "    for feature in ['polarity', 'subjectivity', 'sentiment_std', 'max_polarity', 'min_polarity']:\n", "        df[feature] = sentiment_features.apply(lambda x: x[feature])\n", "    \n", "    # Add sentiment category for easier visualization in Streamlit\n", "    df['sentiment_category'] = df['polarity'].apply(lambda x: \n", "        'Positive' if x > 0.1 else ('Negative' if x < -0.1 else 'Neutral'))\n", "    \n", "    print(\"\\nSentiment features added successfully!\")\n", "    print(\"\\nFeature statistics:\")\n", "    print(df[['polarity', 'subjectivity', 'sentiment_std', 'max_polarity', 'min_polarity']].describe())\n", "    \n", "    # Display sentiment distribution (useful for Streamlit visualization later)\n", "    sentiment_dist = df['sentiment_category'].value_counts()\n", "    print(\"\\nSentiment Distribution:\")\n", "    print(sentiment_dist)\n", "    \n", "    return df\n", "\n", "# Add sentiment features\n", "news_df = analyze_sentiment(news_df)\n", "\n", "# Save the updated dataset\n", "news_df.to_csv('news_with_sentiment.csv', index=False)\n", "print(\"\\nUpdated dataset saved as 'news_with_sentiment.csv'\")"]}, {"cell_type": "code", "execution_count": 35, "id": "f5b44303-6926-4943-9915-b5d3634ccb40", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading required NLTK data...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n", "[nltk_data] Downloading package brown to /Users/<USER>/nltk_data...\n", "[nltk_data]   Unzipping corpora/brown.zip.\n", "[nltk_data] Downloading package wordnet to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package wordnet is already up-to-date!\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Downloading TextBlob corpora...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package brown to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package brown is already up-to-date!\n", "[nltk_data] Downloading package punkt_tab to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Unzipping tokenizers/punkt_tab.zip.\n", "[nltk_data] Downloading package wordnet to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package wordnet is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger_eng to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Unzipping taggers/averaged_perceptron_tagger_eng.zip.\n", "[nltk_data] Downloading package conll2000 to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Unzipping corpora/conll2000.zip.\n", "[nltk_data] Downloading package movie_reviews to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Unzipping corpora/movie_reviews.zip.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finished.\n", "\n", "All required data downloaded successfully!\n"]}], "source": ["import nltk\n", "\n", "# Download required NLTK data\n", "print(\"Downloading required NLTK data...\")\n", "nltk.download('punkt')\n", "nltk.download('averaged_perceptron_tagger')\n", "nltk.download('brown')\n", "nltk.download('wordnet')\n", "\n", "# Download TextBlob corpora\n", "import subprocess\n", "print(\"\\nDownloading TextBlob corpora...\")\n", "subprocess.run(['python', '-m', 'textblob.download_corpora'])\n", "\n", "print(\"\\nAll required data downloaded successfully!\")"]}, {"cell_type": "code", "execution_count": 36, "id": "6fa27428-eba7-4edc-b9df-c37a396a080d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding sentiment analysis features...\n", "Analyzing sentiment for all articles...\n", "Processing article 0/38043 (0.0%)\n", "Processing article 1000/38043 (2.6%)\n", "Processing article 2000/38043 (5.3%)\n", "Processing article 3000/38043 (7.9%)\n", "Processing article 4000/38043 (10.5%)\n", "Processing article 5000/38043 (13.1%)\n", "Processing article 6000/38043 (15.8%)\n", "Processing article 7000/38043 (18.4%)\n", "Processing article 8000/38043 (21.0%)\n", "Processing article 9000/38043 (23.7%)\n", "Processing article 10000/38043 (26.3%)\n", "Processing article 11000/38043 (28.9%)\n", "Processing article 12000/38043 (31.5%)\n", "Processing article 13000/38043 (34.2%)\n", "Processing article 14000/38043 (36.8%)\n", "Processing article 15000/38043 (39.4%)\n", "Processing article 16000/38043 (42.1%)\n", "Processing article 17000/38043 (44.7%)\n", "Processing article 18000/38043 (47.3%)\n", "Processing article 19000/38043 (49.9%)\n", "Processing article 20000/38043 (52.6%)\n", "Processing article 21000/38043 (55.2%)\n", "Processing article 22000/38043 (57.8%)\n", "Processing article 23000/38043 (60.5%)\n", "Processing article 24000/38043 (63.1%)\n", "Processing article 25000/38043 (65.7%)\n", "Processing article 26000/38043 (68.3%)\n", "Processing article 27000/38043 (71.0%)\n", "Processing article 28000/38043 (73.6%)\n", "Processing article 29000/38043 (76.2%)\n", "Processing article 30000/38043 (78.9%)\n", "Processing article 31000/38043 (81.5%)\n", "Processing article 32000/38043 (84.1%)\n", "Processing article 33000/38043 (86.7%)\n", "Processing article 34000/38043 (89.4%)\n", "Processing article 35000/38043 (92.0%)\n", "Processing article 36000/38043 (94.6%)\n", "Processing article 37000/38043 (97.3%)\n", "Processing article 38000/38043 (99.9%)\n", "\n", "Sentiment features added successfully!\n", "\n", "Feature statistics:\n", "           polarity  subjectivity  sentiment_std  max_polarity  min_polarity\n", "count  38043.000000  38043.000000        38043.0  38043.000000  38043.000000\n", "mean       0.043785      0.389206            0.0      0.043785      0.043785\n", "std        0.105381      0.125943            0.0      0.105381      0.105381\n", "min       -1.000000      0.000000            0.0     -1.000000     -1.000000\n", "25%       -0.010000      0.319000            0.0     -0.010000     -0.010000\n", "50%        0.042000      0.391000            0.0      0.042000      0.042000\n", "75%        0.097000      0.463000            0.0      0.097000      0.097000\n", "max        1.000000      1.000000            0.0      1.000000      1.000000\n", "\n", "Sentiment Distribution:\n", "sentiment_category\n", "Neutral     26984\n", "Positive     8886\n", "Negative     2173\n", "Name: count, dtype: int64\n", "\n", "Updated dataset saved as 'news_with_sentiment.csv'\n"]}], "source": ["from textblob import TextBlob\n", "import numpy as np\n", "\n", "def analyze_sentiment(df):\n", "    \"\"\"\n", "    Add sentiment analysis features to the dataset\n", "    \"\"\"\n", "    print(\"Adding sentiment analysis features...\")\n", "    \n", "    def get_sentiment_features(text):\n", "        try:\n", "            # Create TextBlob object\n", "            blob = TextBlob(str(text))\n", "            \n", "            # Get the sentiment polarity (-1 to 1, negative to positive)\n", "            polarity = blob.sentiment.polarity\n", "            \n", "            # Get the subjectivity (0 to 1, objective to subjective)\n", "            subjectivity = blob.sentiment.subjectivity\n", "            \n", "            # Get sentence-level sentiment stats\n", "            sentence_polarities = [sentence.sentiment.polarity for sentence in blob.sentences]\n", "            \n", "            return {\n", "                'polarity': round(polarity, 3),\n", "                'subjectivity': round(subjectivity, 3),\n", "                'sentiment_std': round(np.std(sentence_polarities) if sentence_polarities else 0, 3),\n", "                'max_polarity': round(max(sentence_polarities) if sentence_polarities else 0, 3),\n", "                'min_polarity': round(min(sentence_polarities) if sentence_polarities else 0, 3)\n", "            }\n", "        except Exception as e:\n", "            # Return neutral values in case of any error\n", "            return {\n", "                'polarity': 0,\n", "                'subjectivity': 0,\n", "                'sentiment_std': 0,\n", "                'max_polarity': 0,\n", "                'min_polarity': 0\n", "            }\n", "    \n", "    # Apply sentiment analysis with progress tracking\n", "    print(\"Analyzing sentiment for all articles...\")\n", "    total_rows = len(df)\n", "    \n", "    sentiment_features = []\n", "    for idx, text in enumerate(df['text']):\n", "        if idx % 1000 == 0:  # Print progress every 1000 articles\n", "            print(f\"Processing article {idx}/{total_rows} ({(idx/total_rows*100):.1f}%)\")\n", "        sentiment_features.append(get_sentiment_features(text))\n", "    \n", "    # Convert list of dictionaries to DataFrame\n", "    sentiment_df = pd.DataFrame(sentiment_features)\n", "    \n", "    # Add features to original dataframe\n", "    for column in sentiment_df.columns:\n", "        df[column] = sentiment_df[column]\n", "    \n", "    # Add sentiment category for easier visualization in Streamlit\n", "    df['sentiment_category'] = df['polarity'].apply(lambda x: \n", "        'Positive' if x > 0.1 else ('Negative' if x < -0.1 else 'Neutral'))\n", "    \n", "    print(\"\\nSentiment features added successfully!\")\n", "    print(\"\\nFeature statistics:\")\n", "    print(df[['polarity', 'subjectivity', 'sentiment_std', 'max_polarity', 'min_polarity']].describe())\n", "    \n", "    # Display sentiment distribution\n", "    sentiment_dist = df['sentiment_category'].value_counts()\n", "    print(\"\\nSentiment Distribution:\")\n", "    print(sentiment_dist)\n", "    \n", "    return df\n", "\n", "# Add sentiment features\n", "news_df = analyze_sentiment(news_df)\n", "\n", "# Save the updated dataset\n", "news_df.to_csv('news_with_sentiment.csv', index=False)\n", "print(\"\\nUpdated dataset saved as 'news_with_sentiment.csv'\")"]}, {"cell_type": "code", "execution_count": 37, "id": "c88ccb22-013b-4ae5-ac07-94e7cced247a", "metadata": {}, "outputs": [], "source": ["# Create necessary directories for the Streamlit app\n", "import os\n", "\n", "# Create directories if they don't exist\n", "directories = ['streamlit_app', 'streamlit_app/pages', 'streamlit_app/utils']\n", "for directory in directories:\n", "    os.makedirs(directory, exist_ok=True)"]}, {"cell_type": "code", "execution_count": 40, "id": "fe7e63bb-d6c2-4787-8a04-efc4a4ac254e", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "# Create the streamlit app directory\n", "os.makedirs(\"streamlit_app\", exist_ok=True)"]}, {"cell_type": "code", "execution_count": 41, "id": "1a2bb96c-84f1-4d81-896a-3cc12ae46c6a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-03-18 23:37:54.305 WARNING streamlit.runtime.scriptrunner_utils.script_run_context: Thread 'MainThread': missing ScriptRunContext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.305 WARNING streamlit.runtime.scriptrunner_utils.script_run_context: Thread 'MainThread': missing ScriptRunContext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.343 \n", "  \u001b[33m\u001b[1mWarning:\u001b[0m to view this Streamlit app on a browser, run it with the following\n", "  command:\n", "\n", "    streamlit run /Users/<USER>/Documents/FakeNewsDetection_Development/venv/lib/python3.11/site-packages/ipykernel_launcher.py [ARGUMENTS]\n", "2025-03-18 23:37:54.343 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.858 Thread 'MainThread': missing <PERSON><PERSON><PERSON>RunContext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.858 Thread 'MainThread': missing <PERSON><PERSON><PERSON>RunContext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.858 Thread 'MainThread': missing <PERSON><PERSON><PERSON>RunContext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.859 Thread 'MainThread': missing <PERSON><PERSON><PERSON>Run<PERSON>ontext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.859 Thread 'MainThread': missing <PERSON><PERSON><PERSON>Run<PERSON>ontext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.859 Thread 'MainThread': missing <PERSON><PERSON><PERSON>Run<PERSON>ontext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.859 Thread 'MainThread': missing <PERSON><PERSON><PERSON>Run<PERSON>ontext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.859 Thread 'MainThread': missing <PERSON><PERSON><PERSON>Run<PERSON>ontext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.860 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.860 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.860 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.860 Thread 'MainThread': missing <PERSON><PERSON>tRunContext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.947 Thread 'MainThread': missing <PERSON><PERSON><PERSON>Run<PERSON>ontext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.947 Thread 'MainThread': missing <PERSON><PERSON><PERSON>Run<PERSON>ontext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.947 Thread 'MainThread': missing <PERSON><PERSON><PERSON>Run<PERSON>ontext! This warning can be ignored when running in bare mode.\n", "2025-03-18 23:37:54.948 Thread 'MainThread': missing <PERSON><PERSON><PERSON>Run<PERSON>ontext! This warning can be ignored when running in bare mode.\n"]}], "source": ["import streamlit as st\n", "import pandas as pd\n", "import plotly.express as px\n", "\n", "# Basic page config\n", "st.set_page_config(\n", "    page_title=\"Fake News Detection\",\n", "    page_icon=\"🔍\",\n", "    layout=\"wide\"\n", ")\n", "\n", "def load_data():\n", "    \"\"\"Load the preprocessed news dataset\"\"\"\n", "    try:\n", "        # Try to load the data from the current directory\n", "        df = pd.read_csv(\"news_with_sentiment.csv\")\n", "        return df\n", "    except FileNotFoundError:\n", "        st.error(\"Dataset not found! Please make sure 'news_with_sentiment.csv' exists in the project directory.\")\n", "        return None\n", "\n", "def main():\n", "    # Header\n", "    st.title(\"🔍 Fake News Detection System\")\n", "    \n", "    # Load data\n", "    df = load_data()\n", "    \n", "    if df is not None:\n", "        # Display basic info\n", "        st.write(\"Dataset Overview\")\n", "        st.write(f\"Total number of articles: {len(df)}\")\n", "        \n", "        # Simple visualization\n", "        if 'label' in df.columns:\n", "            st.write(\"Distribution of Articles\")\n", "            fig = px.pie(df, names='label')\n", "            st.plotly_chart(fig)\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": null, "id": "b081d4d5-b4ab-4471-a0c4-6eb31f4bbeb8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}