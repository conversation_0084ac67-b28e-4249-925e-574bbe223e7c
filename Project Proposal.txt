{\rtf1\ansi\ansicpg1252\cocoartf2821
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\froman\fcharset0 Times-Roman;}
{\colortbl;\red255\green255\blue255;\red0\green0\blue0;}
{\*\expandedcolortbl;;\cssrgb\c0\c0\c0;}
\paperw11900\paperh16840\margl1440\margr1440\vieww11520\viewh8400\viewkind0
\deftab720
\pard\pardeftab720\sa240\partightenfactor0

\f0\fs24 \cf0 \expnd0\expndtw0\kerning0
\outl0\strokewidth0 \strokec2 The literature review highlights the growing challenge of fake news and the role of AI in mitigating misinformation. Traditional fact-checking methods are labor-intensive, making automated approaches necessary. This project leverages Natural Language Processing (NLP) and Machine Learning (ML) techniques, including TF-IDF, Word2Vec, GloVe, and transformer models like BERT, RoBERTa, and DistilBERT, to classify news articles as real or fake.\
Explainable AI (XAI) techniques such as SHAP and LIME are integrated to ensure transparency in model predictions. Existing research has demonstrated the effectiveness of transformer models in text classification, but challenges remain in generalizability, dataset diversity, and explainability. This project aims to address these gaps by incorporating multilingual datasets, enhancing interpretability, and ensuring model robustness across different domains.\
The implementation follows an agile methodology, progressing through dataset collection, preprocessing, feature extraction, model training, and explainability integration. The system will be evaluated using accuracy, precision, recall, F1-score, and confusion matrices, with the final goal of developing a user-friendly prototype that can provide actionable insights into fake news patterns.\
}